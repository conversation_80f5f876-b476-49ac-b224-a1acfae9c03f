<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { FeedItem } from '@/stores/transactionalChat/transactionalChatStore'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'
import { useI18n } from 'vue-i18n'
import { useMessage } from 'naive-ui'
import { useActionCardVisibility } from '@/composables/useActionCardVisibility'
import SmartPaymentInfoSection from './SmartPaymentInfoSection.vue'
import SmartNegotiationSection from './SmartNegotiationSection.vue'
import SmartReceiptSection from './SmartReceiptSection.vue'
import SmartPaymentSection from './SmartPaymentSection.vue'
import SmartPaymentWaitingSection from './SmartPaymentWaitingSection.vue'
import SmartPaymentDeclaredSection from './SmartPaymentDeclaredSection.vue'

interface Props {
  item: FeedItem
  autoScroll?: boolean
}

const props = defineProps<Props>()
const { t } = useI18n()
const message = useMessage()
const route = useRoute()
const transactionalChatStore = useTransactionalChatStore()

// Element reference for enhanced visibility tracking
const actionCardRef = ref<HTMLElement | null>(null)

// Enhanced visibility tracking
const {
  observe,
  unobserve,
  isCardVisible,
  isCardFullyVisible,
  getVisibilityState
} = useActionCardVisibility({
  rootMargin: '-60px 0px -80px 0px', // Account for status bar and action bar
  minVisibilityPercent: 25,
  thresholds: [0, 0.25, 0.5, 0.75, 1.0],
  stabilityDelay: 150,
  debug: process.env.NODE_ENV === 'development'
})

// Track this card's visibility and sync with store
const cardVisibilityState = computed(() => getVisibilityState(props.item.id))
const isVisible = computed(() => isCardVisible(props.item.id))
const isFullyVisible = computed(() => isCardFullyVisible(props.item.id))

// Set up observer when component mounts

onMounted(async () => {
  console.log('🔍 ActionCard mounted - actionType:', actionType.value, 'isUsersTurn:', transactionalChatStore.isUsersTurn);

  // Wait for DOM to be ready
  await nextTick()

  if (actionCardRef.value) {
    // Start enhanced visibility tracking
    observe(actionCardRef.value, props.item.id)

    // Sync with store if this is the pinned action
    if (transactionalChatStore.pinnedAction?.cardId === props.item.id) {
      // Initial sync - assume visible on mount
      transactionalChatStore.setActionCardVisibility(true)
    }

    if (props.autoScroll) {
      // Enhanced auto-scroll with proper positioning
      setTimeout(() => {
        if (actionCardRef.value) {
          actionCardRef.value.scrollIntoView({
            block: 'start',
            behavior: 'smooth',
            inline: 'nearest'
          })
        }
      }, 100) // Slightly longer delay for better rendering
    }
  }
})

// Sync visibility state with store
watch(isVisible, (newValue) => {
  if (transactionalChatStore.pinnedAction?.cardId === props.item.id) {
    transactionalChatStore.setActionCardVisibility(newValue)
  }
}, { immediate: true })

// Cleanup on unmount
onUnmounted(() => {
  unobserve(props.item.id)
})

// Computed properties
const actionType = computed(() => props.item.actionType || '')
const otherUser = computed(() => transactionalChatStore.otherUser)
const transactionDetails = computed(() => transactionalChatStore.transactionDetails)
const transactionId = computed(() => route.params.transactionId as string || '')

// Currency filtering for payment methods
const currentTransactionCurrency = computed(() => {
  return transactionDetails.value?.currencyTo || props.item.data?.currency || ''
})

const currencyFilteredPaymentMethods = computed(() => {
  const filteredMethods = props.item.data?.userAllPaymentMethods || []
  const currency = currentTransactionCurrency.value
  
  console.log('🔍 ActionCard - Payment Methods Summary:', {
    actionCardId: props.item.id,
    filteredMethodsCount: filteredMethods.length,
    targetCurrency: currency,
    actionType: props.item.actionType,
    methods: filteredMethods.map((m: any) => ({ 
      id: m.id, 
      type: m.paymentMethodType, 
      currency: m.currency,
      bankName: m.bankName 
    })),
    note: 'Using data from action card props.item.data',
    timestamp: new Date().toISOString()
  })
  
  return filteredMethods
})

// Action card content based on type
const cardTitle = computed(() => {
  const amount = transactionDetails.value?.amountToSend || 0
  const currency = transactionDetails.value?.currencyFrom || 'CAD'
  const otherUserName = otherUser.value?.name || 'User'
  const isUsersTurn = transactionalChatStore.isUsersTurn
  
  switch (actionType.value) {
    case 'paymentInfo':
      return t('transactionalChat.actionCards.paymentInfo.title')
    case 'negotiation':
      return t('transactionalChat.actionCards.negotiation.title')
    case 'confirmReceipt':
      // For receipt confirmation, use the amount user should have received
      const receiptAmount = transactionDetails.value?.amountToReceive || 0
      const receiptCurrency = transactionDetails.value?.currencyTo || 'IRR'
      return t('transactionalChat.actionCards.confirmReceipt.title', { 
        amount: `${receiptAmount} ${receiptCurrency}` 
      })
    case 'confirmFirstPaymentReceipt':
      // For first payment receipt confirmation, use the amount user should have received
      const firstReceiptAmount = transactionDetails.value?.amountToReceive || 0
      const firstReceiptCurrency = transactionDetails.value?.currencyTo || 'IRR'
      return t('transactionalChat.actionCards.confirmFirstPaymentReceipt.title', { 
        amount: `${firstReceiptAmount} ${firstReceiptCurrency}` 
      })
    case 'makeSecondPayment':
      if (isUsersTurn) {
        return t('transactionalChat.actionCards.makeSecondPayment.title', { 
          amount: `${amount} ${currency}`, 
          name: otherUserName 
        })
      } else {
        return t('transactionalChat.actionCards.yourTurnToPay.waitingTitle', { name: otherUserName })
      }
    case 'makePayment':
      if (isUsersTurn) {
        return t('transactionalChat.actionCards.makePayment.title', { 
          amount: `${amount} ${currency}`, 
          name: otherUserName 
        })
      } else {
        return t('transactionalChat.actionCards.yourTurnToPay.waitingTitle', { name: otherUserName })
      }
    case 'rateExperience':
      return t('transactionalChat.actionCards.rateExperience.title', { name: otherUserName })
    case 'paymentDeclared':
      return t('transactionalChat.actionCards.paymentDeclared.title', { 
        amount: `${amount} ${currency}` 
      })
    case 'waiting_makePayment':
      return t('transactionalChat.actionCards.yourTurnToPay.waitingTitle', { name: otherUserName })
    case 'waiting_confirmReceipt':
      return t('transactionalChat.actionCards.paymentDeclared.title', { 
        amount: `${amount} ${currency}` 
      })
    case 'waiting_makeSecondPayment':
      return t('transactionalChat.actionCards.paymentDeclared.title', { 
        amount: `${amount} ${currency}` 
      })
    case 'waiting_confirmFirstPaymentReceipt':
      return t('transactionalChat.actionCards.paymentDeclared.title', { 
        amount: `${amount} ${currency}` 
      })
    default:
      return 'Action Required'
  }
})

const cardDescription = computed(() => {
  const isUsersTurn = transactionalChatStore.isUsersTurn
  
  switch (actionType.value) {
    case 'paymentInfo':
      return t('transactionalChat.actionCards.paymentInfo.description')
    case 'negotiation':
      const otherUserName = otherUser.value?.name || 'User'
      return t('transactionalChat.actionCards.negotiation.systemRecommendation', { name: otherUserName })
    case 'confirmReceipt':
      return t('transactionalChat.actionCards.confirmReceipt.warning')
    case 'confirmFirstPaymentReceipt':
      return t('transactionalChat.actionCards.confirmFirstPaymentReceipt.warning')
    case 'makeSecondPayment':
      if (isUsersTurn) {
        return t('transactionalChat.actionCards.makeSecondPayment.description')
      } else {
        return t('transactionalChat.actionCards.yourTurnToPay.waitingDescription')
      }
    case 'makePayment':
      if (isUsersTurn) {
        return t('transactionalChat.actionCards.makePayment.description')
      } else {
        return t('transactionalChat.actionCards.yourTurnToPay.waitingDescription')
      }
    case 'rateExperience':
      return t('transactionalChat.actionCards.rateExperience.description')
    case 'paymentDeclared':
      const otherUserName2 = otherUser.value?.name || 'User'
      return t('transactionalChat.actionCards.paymentDeclared.description', { 
        amount: `${transactionDetails.value?.amountToSend || 0} ${transactionDetails.value?.currencyFrom || 'CAD'}`,
        recipient: otherUserName2
      })
    case 'waiting_makePayment':
      return t('transactionalChat.actionCards.yourTurnToPay.waitingDescription', { name: otherUser.value?.name || 'User' })
    case 'waiting_confirmReceipt':
      const otherUserName3 = otherUser.value?.name || 'User'
      return t('transactionalChat.actionCards.paymentDeclared.description', { 
        amount: `${transactionDetails.value?.amountToSend || 0} ${transactionDetails.value?.currencyFrom || 'CAD'}`,
        recipient: otherUserName3
      })
    case 'waiting_makeSecondPayment':
      const otherUserName4 = otherUser.value?.name || 'User'
      return t('transactionalChat.actionCards.paymentDeclared.description', { 
        amount: `${transactionDetails.value?.amountToSend || 0} ${transactionDetails.value?.currencyFrom || 'CAD'}`,
        recipient: otherUserName4
      })
    case 'waiting_confirmFirstPaymentReceipt':
      const otherUserName5 = otherUser.value?.name || 'User'
      return t('transactionalChat.actionCards.paymentDeclared.description', { 
        amount: `${transactionDetails.value?.amountToSend || 0} ${transactionDetails.value?.currencyFrom || 'CAD'}`,
        recipient: otherUserName5
      })
    default:
      return ''
  }
})

// Methods
const handlePaymentMethodSelected = (method: any) => {
  console.log('💳 ActionCard - handlePaymentMethodSelected called:', {
    previousMethod: props.item.data?.userCurrentPaymentInfo,
    validationStatus: method?.validationStatus
  })
  
  if (props.item.data) {
    props.item.data.userCurrentPaymentInfo = method;
  }
}

const handlePaymentMethodUpdated = async (method: any) => {
  try {
    // For inline edits, we don't need to call performAction which would trigger modal opening
    // Just update the local state and show success message
    message.success(t('transactionalChat.actionCards.paymentInfo.methodUpdated'))
    
    // Optional: You could emit an event to parent components if needed
    // emit('paymentMethodUpdated', method)
  } catch (error) {
    console.error('Failed to update payment method:', error)
    message.error(t('transactionalChat.actionCards.paymentInfo.methodUpdateFailed'))
  }
}

const handleNewMethodAdded = async (method: any) => {
  try {
    // Persist new payment method to backend
    // Compose payload with only required and allowed fields
    const payload = {
      currency: String(currentTransactionCurrency.value),
      paymentMethodType: 'BANK_TRANSFER',
      bankName: String(method.bankName || ''),
      accountNumber: String(method.accountNumber || ''),
      accountHolderName: String(method.accountHolderName || ''),
      iban: method.iban ? String(method.iban) : undefined,
      swiftCode: method.swiftCode ? String(method.swiftCode) : undefined,
      routingNumber: method.routingNumber ? String(method.routingNumber) : undefined,
      notes: method.notes ? String(method.notes) : undefined
    }
    console.log('[ActionCard] Sending payment method payload:', payload)
    const response = await transactionalChatStore.addPaymentMethod(payload)
    // Update local state with new method
    if (props.item.data) {
      if (!props.item.data.userAllPaymentMethods) {
        props.item.data.userAllPaymentMethods = []
      }
      props.item.data.userAllPaymentMethods.push(response)
      props.item.data.userCurrentPaymentInfo = response
    }
    message.success(t('transactionalChat.actionCards.paymentInfo.methodAdded'))
  } catch (error) {
    if (error?.response) {
      console.error('[ActionCard] Backend error response:', error.response.data)
    }
    console.error('Failed to add payment method:', error)
    message.error(t('transactionalChat.actionCards.paymentInfo.methodAddFailed'))
  }
}

const handlePaymentInfoConfirmed = async (method: any) => {
  console.log('🎯 ActionCard - handlePaymentInfoConfirmed called:', {
    method,
    validationStatus: method?.validationStatus
  })
  
  try {
    // Update local data
    if (props.item.data) {
      props.item.data.userCurrentPaymentInfo = method;
    }
    
    // Confirm payment info with the transaction store to sync all components
    await transactionalChatStore.completePaymentMethodSelection(method.id);
    message.success(t('transactionalChat.actionCards.paymentInfo.infoConfirmed'))
  } catch (error) {
    console.error('Failed to confirm payment info:', error)
    message.error(t('transactionalChat.actionCards.paymentInfo.confirmFailed'))
  }
}

const handleNegotiationUpdate = async (negotiationData: any) => {
  console.log('Negotiation updated:', negotiationData)
  message.success('Negotiation updated successfully')
  // The store will automatically update via socket listeners
}

const handleNegotiationFinalized = async (finalizedPayerId: string) => {
  console.log('Negotiation finalized with payer:', finalizedPayerId)
  message.success(t('transactionalChat.actionCards.negotiation.agreementReached'))
  
  // Refresh the transaction data to get the updated status and next steps
  try {
    // Force refresh with a slight delay to ensure backend has processed the change
    setTimeout(async () => {
      await transactionalChatStore.fetchTransaction(transactionId.value, true)
      console.log('Transaction data refreshed after negotiation finalization')
    }, 500)
  } catch (error) {
    console.error('Failed to refresh transaction details after negotiation finalized:', error)
  }
}

const handleReceiptConfirmed = async () => {
  try {
    const actionToPerform = actionType.value === 'confirmFirstPaymentReceipt' ? 'confirmFirstPayerPayment' : 'confirmReceipt';
    await transactionalChatStore.performAction(actionToPerform)
    message.success(t('transactionalChat.actionCards.confirmReceipt.confirmed'))
  } catch (error) {
    console.error('Failed to confirm receipt:', error)
    message.error(t('transactionalChat.actionCards.confirmReceipt.confirmFailed'))
  }
}

const handleNotReceived = async () => {
  try {
    const actionToPerform = actionType.value === 'confirmFirstPaymentReceipt' ? 'firstPayerPaymentNotReceived' : 'notReceived';
    await transactionalChatStore.performAction(actionToPerform)
    message.info(t('transactionalChat.actionCards.confirmReceipt.notReceivedReported'))
  } catch (error) {
    console.error('Failed to report not received:', error)
    message.error(t('transactionalChat.actionCards.confirmReceipt.reportFailed'))
  }
}

const handlePaymentDeclared = async (data: { referenceNumber?: string; proofFile?: File; notes?: string }) => {
  try {
    const actionToPerform = actionType.value === 'makePayment' ? 'declareFirstPayerPayment' : (actionType.value === 'makeSecondPayment' ? 'declareSecondPayerPayment' : '');
    await transactionalChatStore.performAction(actionToPerform, data)
    message.success(t('transactionalChat.actionCards.yourTurnToPay.declared'))
  } catch (error) {
    console.error('Failed to declare payment:', error)
    message.error(t('transactionalChat.actionCards.yourTurnToPay.declareFailed'))
  }
}

// Computed properties for payment section
const defaultPaymentInstructions = computed(() => ({
  steps: [
    t('transactionalChat.actionCards.yourTurnToPay.step1'),
    t('transactionalChat.actionCards.yourTurnToPay.step2'),
    t('transactionalChat.actionCards.yourTurnToPay.step3'),
    t('transactionalChat.actionCards.yourTurnToPay.step4')
  ],
  estimatedTime: t('transactionalChat.actionCards.yourTurnToPay.estimatedTime')
}))

const defaultRecipientDetails = computed(() => ({
  bankName: mockPaymentDetails.value.bankName,
  accountNumber: mockPaymentDetails.value.accountNumber,
  accountHolderName: mockPaymentDetails.value.recipientName,
  transitNumber: mockPaymentDetails.value.transitNumber
}))

const handlePrimaryAction = async () => {
  try {
    await transactionalChatStore.performAction(actionType.value)
  } catch (error) {
    console.error('Failed to perform action:', error)
  }
}

const handleContactSupport = async () => {
  console.log('Contact support requested for payment declaration')
  try {
    // TODO: Implement support contact functionality
    message.info('Support will be contacted shortly')
  } catch (error) {
    console.error('Failed to contact support:', error)
    message.error('Failed to contact support')
  }
}

const handleCancelPayment = async () => {
  console.log('Cancel payment requested')
  try {
    // TODO: Implement payment cancellation functionality
    await transactionalChatStore.performAction('cancelPayment')
    message.success('Payment cancellation requested')
  } catch (error) {
    console.error('Failed to cancel payment:', error)
    message.error('Failed to cancel payment')
  }
}

// Mock data for development - TODO: Replace with real data from backend
const mockPaymentDetails = computed(() => ({
  bankName: 'TD Canada Trust', // TODO: Get from transactionDetails.otherUserPaymentDetails
  accountNumber: '1234-567-890',
  transitNumber: '12345',
  recipientName: otherUser.value?.name || 'Alice Johnson'
}));

// Determine whether to show the card header
const shouldShowCardHeader = computed(() => {
  // Always hide header for negotiation
  if (actionType.value === 'negotiation') {
    return false;
  }
  
  // Hide header for waiting states in payment actions (they have their own internal headers)
  if ((actionType.value === 'makePayment' || actionType.value === 'makeSecondPayment') && !transactionalChatStore.isUsersTurn) {
    return false;
  }
  
  // Hide header for payment declared and waiting variations (they have their own internal headers)
  if (actionType.value === 'paymentDeclared' || 
      actionType.value === 'waiting_confirmReceipt' || 
      actionType.value === 'waiting_confirmFirstPaymentReceipt') {
    return false;
  }
  
  // Hide header for waiting states that use SmartPaymentWaitingSection
  if (actionType.value === 'waiting_makePayment' || 
      actionType.value === 'waiting_makeSecondPayment') {
    return false;
  }
  
  // Show header for all other cases
  return true;
});</script>

<template>
  <div
    ref="actionCardRef"
    class="action-card"
    :class="[
      `action-card-${actionType}`,
      {
        'action-card--visible': isVisible,
        'action-card--fully-visible': isFullyVisible,
        'action-card--stable': cardVisibilityState?.isStable
      }
    ]"
    :data-card-id="item.id"
    data-testid="action-card"
  >
    <div class="card-content">
      <!-- Card Header (conditionally shown based on action type and user state) -->
      <div v-if="shouldShowCardHeader" class="card-header">
        <h3 
          class="card-title"
          data-testid="action-card-title"
        >
          {{ cardTitle }}
        </h3>
        <p 
          v-if="cardDescription"
          class="card-description"
          data-testid="action-card-description"
        >
          {{ cardDescription }}
        </p>
      </div>
      
      <!-- Payment Info Card -->
      <div 
        v-if="actionType === 'paymentInfo'"
        class="card-body payment-info-body"
      >
        <SmartPaymentInfoSection
          :existing-methods="currencyFilteredPaymentMethods"
          :current-method="item.data?.userCurrentPaymentInfo"
          :currency="currentTransactionCurrency"
          :can-edit-inline="item.data?.canEditInline !== false"
          @method-selected="handlePaymentMethodSelected"
          @method-updated="handlePaymentMethodUpdated"
          @new-method-added="handleNewMethodAdded"
          @payment-info-confirmed="handlePaymentInfoConfirmed"
        />
      </div>
      
      <!-- Negotiation Card -->
      <div 
        v-else-if="actionType === 'negotiation'"
        class="card-body"
      >
        <SmartNegotiationSection
          :transaction-id="transactionId"
          @negotiation-update="handleNegotiationUpdate"
          @negotiation-finalized="handleNegotiationFinalized"
        />
      </div>
      
      <!-- Confirm Receipt Card -->
      <div 
        v-else-if="actionType === 'confirmReceipt'"
        class="card-body"
      >
        <SmartReceiptSection
          :payment-declaration="{
            amount: item.data?.amount || transactionDetails?.amountToReceive || 45000000,
            currency: item.data?.currency || transactionDetails?.currencyTo || 'IRR',
            declaredBy: otherUser?.name || 'Other User',
            declaredAt: item.data?.declaredAt || new Date().toISOString(),
            trackingNumber: item.data?.referenceNumber,
            reference: item.data?.notes
          }"
          :chat-session-id="transactionalChatStore.chatSessionId"
          @receipt-confirmed="handleReceiptConfirmed"
          @not-received="handleNotReceived"
        />
      </div>
      
      <!-- Confirm First Payment Receipt Card -->
      <div 
        v-else-if="actionType === 'confirmFirstPaymentReceipt'"
        class="card-body"
      >
        <SmartReceiptSection
          :payment-declaration="{
            amount: item.data?.amount || transactionDetails?.amountToReceive || 45000000,
            currency: item.data?.currency || transactionDetails?.currencyTo || 'IRR',
            declaredBy: otherUser?.name || 'Other User',
            declaredAt: item.data?.declaredAt || new Date().toISOString(),
            trackingNumber: item.data?.referenceNumber,
            reference: item.data?.notes
          }"
          :chat-session-id="transactionalChatStore.chatSessionId"
          @receipt-confirmed="handleReceiptConfirmed"
          @not-received="handleNotReceived"
        />
      </div>
      
      <!-- Make Payment Card (Step 3) -->
      <div 
        v-else-if="actionType === 'makePayment'"
        class="card-body"
      >
        <template v-if="transactionalChatStore.isUsersTurn">
          <SmartPaymentSection
            :amount="item.data?.amount || transactionDetails?.amountToSend || 500"
            :currency="item.data?.currency || transactionDetails?.currencyFrom || 'CAD'"
            :recipient-details="item.data?.recipientDetails || defaultRecipientDetails"
            :instructions="item.data?.instructions || defaultPaymentInstructions"
            :can-upload-proof="true"
            :requires-reference="true"
            :chat-session-id="transactionalChatStore.chatSessionId"
            @payment-declared="handlePaymentDeclared"
          />
        </template>
        <template v-else>
          <SmartPaymentWaitingSection />
        </template>
      </div>
      
      <!-- Your Turn to Pay Card (Step 5) -->
      <div 
        v-else-if="actionType === 'makeSecondPayment'"
        class="card-body"
      >
        <template v-if="transactionalChatStore.isUsersTurn">
          <SmartPaymentSection
            :amount="item.data?.amount || transactionDetails?.amountToSend || 500"
            :currency="item.data?.currency || transactionDetails?.currencyFrom || 'CAD'"
            :recipient-details="item.data?.recipientDetails || defaultRecipientDetails"
            :instructions="item.data?.instructions || defaultPaymentInstructions"
            :can-upload-proof="true"
            :requires-reference="true"
            :chat-session-id="transactionalChatStore.chatSessionId"
            @payment-declared="handlePaymentDeclared"
          />
        </template>
        <template v-else>
          <SmartPaymentWaitingSection />
        </template>
      </div>
      
      <!-- Rate Experience Card -->
      <div 
        v-else-if="actionType === 'rateExperience'"
        class="card-body"
      >
        <div class="rating-section">
          <div class="star-rating">
            <button 
              v-for="star in 5"
              :key="star"
              class="star-button"
              :class="{ 'star-active': star <= 4 }"
              data-testid="star-button"
            >
              ⭐
            </button>
          </div>
        </div>
        
        <button 
          class="primary-button"
          data-testid="submit-rating-button"
          @click="handlePrimaryAction"
        >
          {{ t('transactionalChat.actionCards.rateExperience.submitButton') }}
        </button>
      </div>
      
      <!-- Payment Declared Card (waiting for other user to confirm receipt) -->
      <div 
        v-else-if="actionType === 'paymentDeclared'"
        class="card-body"
      >
        <SmartPaymentDeclaredSection
          :payment-declaration="{
            amount: item.data?.amount || transactionDetails?.amountToSend || 500,
            currency: item.data?.currency || transactionDetails?.currencyFrom || 'CAD',
            declaredBy: 'You',
            declaredAt: item.data?.declaredAt || new Date().toISOString(),
            trackingNumber: item.data?.trackingNumber,
            reference: item.data?.reference
          }"
          :recipient-name="otherUser?.name || 'Other User'"
          :chat-session-id="transactionalChatStore.chatSessionId"
          :can-contact-support="item.data?.canContactSupport !== false"
          :can-cancel-payment="item.data?.canCancelPayment === true"
          @contact-support="handleContactSupport"
          @cancel-payment="handleCancelPayment"
        />
      </div>
      
      <!-- Waiting for other user to make payment -->
      <div 
        v-else-if="actionType === 'waiting_makePayment'"
        class="card-body"
      >
        <SmartPaymentWaitingSection />
      </div>
      
      <!-- Waiting for other user to confirm receipt (showing user's declared payment) -->
      <div 
        v-else-if="actionType === 'waiting_confirmReceipt'"
        class="card-body"
      >
        <SmartPaymentDeclaredSection
          :payment-declaration="{
            amount: item.data?.amount || transactionDetails?.amountToSend || 500,
            currency: item.data?.currency || transactionDetails?.currencyFrom || 'CAD',
            declaredBy: 'You',
            declaredAt: item.data?.declaredAt || new Date().toISOString(),
            trackingNumber: item.data?.trackingNumber,
            reference: item.data?.reference
          }"
          :recipient-name="otherUser?.name || 'Other User'"
          :chat-session-id="transactionalChatStore.chatSessionId"
          :can-contact-support="item.data?.canContactSupport !== false"
          :can-cancel-payment="item.data?.canCancelPayment === true"
          @contact-support="handleContactSupport"
          @cancel-payment="handleCancelPayment"
        />
      </div>
      
      <!-- Waiting for other user to make second payment (showing user's status) -->
      <div 
        v-else-if="actionType === 'waiting_makeSecondPayment'"
        class="card-body"
      >
        <SmartPaymentWaitingSection />
      </div>
      
      <!-- Waiting for other user to confirm receipt of second payment (showing user's declared payment) -->
      <div 
        v-else-if="actionType === 'waiting_confirmFirstPaymentReceipt'"
        class="card-body"
      >
        <SmartPaymentDeclaredSection
          :payment-declaration="{
            amount: item.data?.amount || transactionDetails?.amountToSend || 500,
            currency: item.data?.currency || transactionDetails?.currencyFrom || 'CAD',
            declaredBy: 'You',
            declaredAt: item.data?.declaredAt || new Date().toISOString(),
            trackingNumber: item.data?.trackingNumber,
            reference: item.data?.reference
          }"
          :recipient-name="otherUser?.name || 'Other User'"
          :chat-session-id="transactionalChatStore.chatSessionId"
          :can-contact-support="item.data?.canContactSupport !== false"
          :can-cancel-payment="item.data?.canCancelPayment === true"
          @contact-support="handleContactSupport"
          @cancel-payment="handleCancelPayment"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.action-card {
  /* Constrained width and centered layout */
  max-width: 95%;
  margin: 24px auto; /* Increased vertical margin and auto for horizontal centering */
  
  /* Glassmorphism effect */
  background: rgba(var(--n-card-color-rgb), 0.7); /* Use Naive UI's card color with transparency */
  backdrop-filter: blur(12px) saturate(150%);
  -webkit-backdrop-filter: blur(12px) saturate(150%);
  
  /* Enhanced border and shadow for depth */
  border: 1px solid rgba(var(--n-border-color-rgb), 0.25);
  border-radius: 20px; /* Slightly larger radius for a softer look */
  box-shadow: var(--n-box-shadow2); /* Leverage Naive UI's shadow system for consistency */

  overflow: hidden;
  animation: slideInUp 0.4s ease-out;
  transition: all 0.3s ease;
  position: relative; /* Required for the accent bar */
}

/* Visual Accent Bar */
.action-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 20px;
  bottom: 20px;
  width: 4px;
  background: var(--tc-primary);
  border-radius: 0 4px 4px 0;
  opacity: 0.9;
  transition: all 0.3s ease;
}

.action-card:hover::before {
  opacity: 1;
  transform: scaleX(1.1);
}

.card-content {
  /* Adjust padding to account for the accent bar */
  padding: 20px 20px 20px 28px;
}

.card-header {
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--tc-text-primary);
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.card-description {
  font-size: 14px;
  color: var(--tc-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* --- Previous styles for buttons, details, etc. remain unchanged --- */

/* Buttons */
.primary-button {
  background-color: var(--tc-primary);
  color: var(--tc-text-white);
  border: none;
  border-radius: 12px;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
  width: 100%;
}

.primary-button:hover {
  background-color: var(--tc-primary-hover);
  transform: translateY(-1px);
}

.primary-button:active {
  background-color: var(--tc-primary-active);
  transform: translateY(0);
}

.primary-button.danger {
  background-color: var(--tc-warning);
}

.primary-button.danger:hover {
  background-color: var(--tc-warning-hover);
}

.secondary-button {
  background-color: transparent;
  color: var(--tc-primary);
  border: 2px solid var(--tc-primary);
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
  flex: 1;
}

.secondary-button:hover {
  background-color: var(--tc-primary);
  color: var(--tc-text-white);
}

.button-group {
  display: flex;
  gap: 12px;
  flex-direction: column;
}

/* Payment Details */
.payment-details {
  background-color: var(--tc-bg-secondary);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid var(--tc-border-light);
}

.details-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--tc-text-primary);
  margin: 0 0 12px 0;
}

.details-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.detail-label {
  font-size: 14px;
  color: var(--tc-text-muted);
  font-weight: 500;
  min-width: 60px;
}

.detail-value {
  font-size: 14px;
  color: var(--tc-text-primary);
  font-weight: 600;
  flex: 1;
  text-align: right;
}

.copy-button {
  background-color: var(--tc-border-light);
  color: var(--tc-text-secondary);
  border: none;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.copy-button:hover {
  background-color: var(--tc-border-medium);
}

/* Star Rating */
.rating-section {
  text-align: center;
}

.star-rating {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.star-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  transition: transform 0.2s ease;
  opacity: 0.4;
}

.star-button.star-active {
  opacity: 1;
}

.star-button:hover {
  transform: scale(1.1);
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Highlight effect when scrolled to from pinned banner */
.action-card.highlight-card {
  box-shadow: 0 0 0 3px var(--tc-primary), var(--tc-action-card-shadow);
  transform: scale(1.02);
  transition: all 0.3s ease;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .action-card {
    max-width: 100%; /* On mobile, allow it to be a bit wider */
  }
  .card-content {
    padding: 16px 16px 16px 24px; /* Adjust mobile padding */
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .card-description {
    font-size: 13px;
  }
  
  .primary-button,
  .secondary-button {
    font-size: 15px;
    padding: 12px 16px;
    min-height: 44px;
  }
  
  .payment-details {
    padding: 12px;
  }
  
  .details-title {
    font-size: 13px;
  }
  
  .detail-label,
  .detail-value {
    font-size: 13px;
  }
  
  .copy-button {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .star-button {
    font-size: 20px;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .card-content {
    padding: 12px 12px 12px 20px;
  }
  
  .card-title {
    font-size: 15px;
  }
  
  .card-description {
    font-size: 12px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-value {
    text-align: left;
  }
  
  .copy-button {
    align-self: flex-end;
  }
}

/* Enhanced visibility states */
.action-card--visible {
  /* Subtle indication when card is visible */
  box-shadow: var(--n-box-shadow3);
}

.action-card--fully-visible {
  /* Enhanced styling when card is fully visible */
  border-color: rgba(var(--n-primary-color-rgb), 0.4);
}

.action-card--stable {
  /* Stable state - no rapid visibility changes */
  transition: all 0.3s ease;
}

/* Scroll highlight effect */
.scroll-highlight {
  animation: scrollHighlight 2s ease-out;
  border-color: var(--tc-primary) !important;
  box-shadow: 0 0 0 2px rgba(var(--n-primary-color-rgb), 0.3), var(--n-box-shadow3) !important;
}

@keyframes scrollHighlight {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--n-primary-color-rgb), 0.7), var(--n-box-shadow3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 8px rgba(var(--n-primary-color-rgb), 0.3), var(--n-box-shadow3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 2px rgba(var(--n-primary-color-rgb), 0.3), var(--n-box-shadow3);
  }
}

/* RTL Support */
[dir="rtl"] .action-card::before {
  left: auto;
  right: 0;
  border-radius: 4px 0 0 4px;
}
[dir="rtl"] .card-content {
  direction: rtl;
  padding: 20px 28px 20px 20px;
}

[dir="rtl"] .detail-item {
  direction: rtl;
}

[dir="rtl"] .detail-value {
  text-align: left;
}

[dir="rtl"] .copy-button {
  margin-left: 0;
  margin-right: auto;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .action-card {
    border-width: 2px;
    background: var(--n-body-color); /* Use solid color for high contrast */
    backdrop-filter: none;
  }
  
  .primary-button {
    border: 2px solid var(--tc-primary);
  }
  
  .payment-details {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .action-card {
    animation: none;
  }
  
  .primary-button:hover,
  .star-button:hover {
    transform: none;
  }
}
</style>