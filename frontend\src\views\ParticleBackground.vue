<template>
  <div class="bg-particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
  </div>
</template>

<script setup lang="ts">
// No logic needed for static background
</script>

<style scoped>
.bg-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
}
/* Animated background particles */
.bg-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) { width: 80px; height: 80px; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 120px; height: 120px; left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { width: 60px; height: 60px; left: 60%; animation-delay: 2s; }
.particle:nth-child(4) { width: 100px; height: 100px; left: 80%; animation-delay: 3s; }
.particle:nth-child(5) { width: 40px; height: 40px; left: 40%; animation-delay: 4s; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.5; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

</style>
