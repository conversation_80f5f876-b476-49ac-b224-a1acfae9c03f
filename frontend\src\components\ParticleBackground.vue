<template>
  <div class="bg-particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
  </div>
</template>

<script setup lang="ts">
// No logic needed for static background
</script>

<style scoped>
.bg-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
  /* Add your background styling here */
}
.particle {
  /* Add your particle animation and styling here */
}
</style>
